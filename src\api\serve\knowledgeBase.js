import request from "@/utils/request";

// 查询知识库主列表
export function getClassifyData(query) {
    return request({
        url: "/tyywpt/tTyywZsk/getLibraryTree?needLibrary=true",
        method: "get",
        params: query,
    });
}
// 新增编辑知识库
export function editClassifyData(data) {
    return request({
        url: "/tyywpt/tTyywZsk/editLibrary",
        method: "post",
        data
    });
}
// 获取知识库详情
export function getClassifyDataDetail(id) {
    return request({
        url: "/tyywpt/tTyywZsk/queryLibraryDetail/" + id,
        method: "get",
    });
}

// 新的知识库接口------------------------
// 获取知识库树形结构
export function getLibraryTree(query) {
    return request({
        url: "/tyywpt/tTyywZskNew/getLibraryTree",
        method: "get",
        params: query,
    });
}