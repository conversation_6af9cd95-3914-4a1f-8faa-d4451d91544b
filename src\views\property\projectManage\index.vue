<template>
  <div class="container">
    <!-- 查询条件卡片 -->
    <div class="card">
      <div class="cardTitle">项目管理</div>
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="项目编号" prop="projectNo">
          <el-input
            v-model="queryParams.projectNo"
            placeholder="请输入项目编号"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input
            v-model="queryParams.projectName"
            placeholder="请输入项目名称"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="项目类型" prop="projectType">
          <el-select
            v-model="queryParams.projectType"
            placeholder="请选择项目类型"
            clearable
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="属地" prop="sd">
          <el-select
            v-model="queryParams.sd"
            placeholder="请选择属地"
            clearable
          >
            <el-option
              v-for="item in sdOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目状态" prop="projectStatus">
          <el-select
            v-model="queryParams.projectStatus"
            placeholder="请选择项目类型"
            clearable
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="建设单位" prop="constructionUnitId">
          <treeselect
            style="width: 160px; height: 32px"
            v-model="queryParams.constructionUnitId"
            :options="enabledDeptOptions"
            :show-count="true"
            placeholder="请选择建设单位"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮卡片 -->
    <div class="card">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            >删除</el-button
          > </el-col
        ><el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-upload2"
            size="mini"
            @click="handleImport"
            >导入</el-button
          ></el-col
        >
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
    </div>

    <!-- 数据表格卡片 -->
    <div class="card">
      <el-table
        v-loading="loading"
        :data="supplyChainList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="项目编号" align="center" prop="projectNo" />
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="项目类型" align="center" prop="projectType" />
        <el-table-column label="属地" align="center" prop="sd" />
        <el-table-column
          label="立项时间"
          align="center"
          prop="projectEstablishTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.projectEstablishTime, "{y}-{m}-{d}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目状态" align="center" prop="projectStatus" />
        <el-table-column
          label="建设单位"
          align="center"
          prop="constructionUnit"
        />
        <el-table-column label="开发厂商" align="center" prop="kfcs" />
        <el-table-column label="运维厂商" align="center" prop="ywcs" />
        <el-table-column label="安全厂商" align="center" prop="aqcs" />
        <el-table-column label="运维人员" align="center" prop="ywry" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 项目导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <!-- <div class="el-upload__tip" slot="tip">
            <el-checkbox
              v-model="upload.updateSupport"
            />是否更新已经存在的项目数据
          </div> -->
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProject,
  getProject,
  delProject,
  listSd,
} from "@/api/property/projectManage";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { deptTreeSelect } from "@/api/serve/orderlist";
import { getToken } from "@/utils/auth";

export default {
  name: "SupplyChainManage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应链管理表格数据
      supplyChainList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectNo: null,
        projectName: null,
        projectType: null,
        sd: null,
        projectStatus: null,
        constructionUnitId: undefined,
      },
      typeOptions: [
        { label: "软件", value: "软件" },
        { label: "硬件", value: "硬件" },
        { label: "其他", value: "其他" },
      ],
      statusOptions: [
        { label: "开发中", value: "开发中" },
        { label: "试运行", value: "试运行" },
        { label: "运行中", value: "运行中" },
        { label: "已下线", value: "已下线" },
      ],
      sdOptions: [],
      enabledDeptOptions: [],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/tyywpt/tTyywProject/importData",
      },
    };
  },
  components: { Treeselect },
  created() {
    this.init();
    this.getList();
  },
  methods: {
    init() {
      /** 查询部门下拉树结构 */
      deptTreeSelect().then((response) => {
        this.enabledDeptOptions = this.filterDisabledDept(
          JSON.parse(JSON.stringify(response.data))
        );
      });
      listSd().then((res) => {
        this.sdOptions = res.data.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      });
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        "tyywpt/tTyywProject/mb",
        {},
        `project_template_${new Date().getTime()}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 查询供应链管理列表 */
    getList() {
      this.loading = true;
      listProject(this.queryParams)
        .then((response) => {
          const resdata = response.data.list;
          this.supplyChainList = resdata;
          this.total = response.data.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        projectNo: null,
        projectName: null,
        projectType: null,
        sd: null,
        projectStatus: null,
        constructionUnitId: undefined,
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // 过滤掉无效的id
      this.ids = selection
        .map((item) => item.id)
        .filter((id) => id != null && id !== undefined);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // 跳转到应用新增页面
      this.$router.push({
        path: "projectManageEdit",
        query: {
          mode: "add",
        },
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let id;

      if (row && row.id) {
        // 从行操作按钮点击，直接使用行数据的id
        id = row.id;
      } else if (this.ids && this.ids.length === 1) {
        // 从工具栏按钮点击，使用选中的第一个id
        id = this.ids[0];
      } else {
        // 没有选中数据或选中多条数据
        this.$message.error("请选择一条数据进行修改");
        return;
      }

      if (!id) {
        this.$message.error("无效的数据ID");
        return;
      }

      getProject(id)
        .then((response) => {
          let resdata = response.data;
          // 跳转到项目编辑页面
          this.$router.push({
            path: "projectManageEdit",
            query: {
              id: id,
              formData: resdata,
            },
          });
        })
        .catch((error) => {
          console.error("获取供应链数据失败:", error);
          this.$message.error("获取数据失败");
        });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let ids;

      if (row && row.id) {
        // 从行操作按钮点击，删除单条数据
        ids = [row.id];
      } else if (this.ids && this.ids.length > 0) {
        // 从工具栏按钮点击，删除选中的数据
        ids = this.ids;
      } else {
        this.$message.error("请选择要删除的数据");
        return;
      }

      // 过滤掉无效的id
      const validIds = ids.filter((id) => id != null && id !== undefined);
      if (validIds.length === 0) {
        this.$message.error("无效的数据ID");
        return;
      }

      const idsStr = validIds.join(",");
      this.$modal
        .confirm("是否确认删除项目？")
        .then(() => {
          return delProject(idsStr);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch((error) => {
          console.error("删除失败:", error);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
}

.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;

  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
</style>
